{% extends "base.html" %}

{% block title %}Date Analysis Report - Archives Raw Data Finder{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="bi bi-calendar-check"></i> Date Analysis Report</h1>
                <a href="{{ url_for('admin') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Admin
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-primary text-white">
                <div class="card-body">
                    <i class="bi bi-collection display-4 mb-2"></i>
                    <h3>{{ summary.total_sheets }}</h3>
                    <p class="mb-0">Total Sheets</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <i class="bi bi-calendar-check display-4 mb-2"></i>
                    <h3>{{ summary.sheets_with_dates }}</h3>
                    <p class="mb-0">Sheets with Dates</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-warning text-dark">
                <div class="card-body">
                    <i class="bi bi-calendar-x display-4 mb-2"></i>
                    <h3>{{ summary.sheets_without_dates }}</h3>
                    <p class="mb-0">Sheets without Dates</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <i class="bi bi-arrow-repeat display-4 mb-2"></i>
                    <h3>{{ "{:,}".format(summary.total_dates_processed) }}</h3>
                    <p class="mb-0">Dates Standardized</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analysis -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-table"></i> Sheet-by-Sheet Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Sheet Name</th>
                                    <th>Original Column Name</th>
                                    <th>Column Position</th>
                                    <th>Standardized Column Name</th>
                                    <th>Dates Processed</th>
                                    <th>Sample Conversions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sheet_name, analysis in date_analysis.items() %}
                                <tr>
                                    <td>
                                        <strong>{{ sheet_name }}</strong>
                                    </td>
                                    <td>
                                        {% if analysis.column_name %}
                                            <span class="badge bg-primary">{{ analysis.column_name }}</span>
                                        {% else %}
                                            <span class="text-muted">No date column found</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if analysis.column_index is not none %}
                                            Column {{ analysis.column_index + 1 }} 
                                            {% if analysis.column_index == 3 %}
                                                <span class="badge bg-success">D</span>
                                            {% elif analysis.column_index == 4 %}
                                                <span class="badge bg-info">E</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ (analysis.column_index + 65) | chr }}</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if analysis.standardized_column_name %}
                                            <code>{{ analysis.standardized_column_name }}</code>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if analysis.dates_processed > 0 %}
                                            <span class="badge bg-success">{{ analysis.dates_processed }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if analysis.sample_conversions %}
                                            <button class="btn btn-sm btn-outline-info" type="button" 
                                                    data-bs-toggle="collapse" 
                                                    data-bs-target="#samples-{{ loop.index }}" 
                                                    aria-expanded="false">
                                                <i class="bi bi-eye"></i> View Samples
                                            </button>
                                            <div class="collapse mt-2" id="samples-{{ loop.index }}">
                                                <div class="card card-body">
                                                    <small>
                                                        {% for original, standardized in analysis.sample_conversions %}
                                                        <div class="mb-1">
                                                            <strong>Before:</strong> <code>{{ original }}</code><br>
                                                            <strong>After:</strong> <code>{{ standardized }}</code>
                                                        </div>
                                                        {% if not loop.last %}<hr class="my-2">{% endif %}
                                                        {% endfor %}
                                                    </small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">No samples</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Format Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> Date Standardization Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Standardization Process:</h6>
                            <ul>
                                <li>All dates are converted to <strong>DD-MMM-YYYY</strong> format</li>
                                <li>Two-digit years are handled intelligently (00-30 → 2000-2030, 31-99 → 1931-1999)</li>
                                <li>Various input formats are supported (e.g., "Apr 3, 09", "03-Apr-2009", "2009-04-03")</li>
                                <li>Invalid dates are left unchanged</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Column Detection Logic:</h6>
                            <ul>
                                <li>Prioritizes Column D and Column E</li>
                                <li>Checks columns with date-related names</li>
                                <li>Analyzes content to confirm date presence</li>
                                <li>Requires at least 30% of values to be valid dates</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <h6><i class="bi bi-lightbulb"></i> Benefits of Standardization</h6>
                        <p class="mb-0">
                            With standardized dates, the search functionality can now accurately find dates within ±9 days 
                            of your search query, regardless of the original format in the Excel sheets. This ensures 
                            consistent and reliable date-based searching across all archive categories.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
