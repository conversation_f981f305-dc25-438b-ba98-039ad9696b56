{% extends "base.html" %}

{% block title %}Home - Archives Raw Data Finder{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="search-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 mb-3">
                    <i class="bi bi-archive"></i> Archives Raw Data Finder
                </h1>
                <p class="lead mb-4">
                    Search and analyze raw footage data across multiple archive categories with powerful filtering and sorting capabilities.
                </p>
                <a href="{{ url_for('search') }}" class="btn btn-light btn-lg">
                    <i class="bi bi-search"></i> Start Searching
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="bi bi-collection display-4 mb-2"></i>
                    <h3 class="card-title">{{ stats.total_sheets }}</h3>
                    <p class="card-text">Archive Categories</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="bi bi-database display-4 mb-2"></i>
                    <h3 class="card-title">{{ "{:,}".format(stats.total_records) }}</h3>
                    <p class="card-text">Total Records</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="bi bi-people display-4 mb-2"></i>
                    <h3 class="card-title">{{ stats.user_count }}</h3>
                    <p class="card-text">Registered Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="bi bi-clock-history display-4 mb-2"></i>
                    <h3 class="card-title">24/7</h3>
                    <p class="card-text">Available</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Search -->
    <div class="row mb-4">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-lightning"></i> Quick Search</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('search') }}" method="GET">
                        <div class="input-group">
                            <input type="text" class="form-control form-control-lg"
                                   name="q" placeholder="Search for events, locations, dates, or any keyword...">
                            <button class="btn btn-primary btn-lg" type="submit">
                                <i class="bi bi-search"></i> Search
                            </button>
                        </div>
                    </form>

                    <!-- Example Searches -->
                    <div class="mt-3">
                        <small class="text-muted">Try these example searches:</small>
                        <div class="mt-2">
                            <a href="{{ url_for('search') }}?q=Hindi" class="btn btn-outline-secondary btn-sm me-2 mb-1">Hindi</a>
                            <a href="{{ url_for('search') }}?q=YouTube Hindi Mahashivratri" class="btn btn-outline-secondary btn-sm me-2 mb-1">YouTube Hindi Mahashivratri</a>
                            <a href="{{ url_for('search') }}?q=03-Apr-2009" class="btn btn-outline-secondary btn-sm me-2 mb-1">03-Apr-2009</a>
                            <a href="{{ url_for('search') }}?q=Sadhguru" class="btn btn-outline-secondary btn-sm me-2 mb-1">Sadhguru</a>
                            <a href="{{ url_for('search') }}?q=English&column=audio" class="btn btn-outline-secondary btn-sm me-2 mb-1">English (Audio)</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Archive Categories -->
    {% if sheet_names %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-folder2-open"></i> Archive Categories</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for sheet in sheet_names[:12] %}
                        <div class="col-md-6 col-lg-4 mb-2">
                            <a href="{{ url_for('search') }}?sheet={{ sheet }}" 
                               class="btn btn-outline-primary btn-sm w-100 text-start">
                                <i class="bi bi-folder"></i> {{ sheet }}
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                    {% if sheet_names|length > 12 %}
                    <div class="text-center mt-3">
                        <a href="{{ url_for('search') }}" class="btn btn-link">
                            View all {{ sheet_names|length }} categories →
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Features -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-search display-4 text-primary mb-3"></i>
                    <h5 class="card-title">Advanced Search</h5>
                    <p class="card-text">
                        Search across all archive categories with case-insensitive partial matching. 
                        Filter by sheet, column, and sort results.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-lightning display-4 text-warning mb-3"></i>
                    <h5 class="card-title">Fast Performance</h5>
                    <p class="card-text">
                        Optimized for speed with in-memory caching and efficient data processing. 
                        Get results in under 1 second.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="bi bi-shield-check display-4 text-success mb-3"></i>
                    <h5 class="card-title">Secure Access</h5>
                    <p class="card-text">
                        Multi-user authentication system with admin controls and activity logging 
                        for secure data access.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> System Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Data Source:</strong> 
                                {% if stats.last_processed %}
                                    {{ stats.last_processed.split('/')[-1] if '/' in stats.last_processed else stats.last_processed }}
                                {% else %}
                                    No file processed yet
                                {% endif %}
                            </p>
                            <p><strong>Total Categories:</strong> {{ stats.total_sheets }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Total Records:</strong> {{ "{:,}".format(stats.total_records) }}</p>
                            <p><strong>System Status:</strong> 
                                <span class="badge bg-success">Online</span>
                            </p>
                        </div>
                    </div>
                    
                    {% if stats.is_admin %}
                    <div class="mt-3">
                        <a href="{{ url_for('admin') }}" class="btn btn-warning">
                            <i class="bi bi-gear"></i> Admin Panel
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
