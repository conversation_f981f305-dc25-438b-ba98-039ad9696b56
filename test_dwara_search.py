#!/usr/bin/env python3
"""
Test script for Dwara search functionality
"""

import pandas as pd
import os

def test_dwara_search():
    """Test the dwara search functionality"""
    
    print("Testing Dwara Search Functionality")
    print("=" * 50)
    
    # Check if dwara.csv exists
    if not os.path.exists('dwara.csv'):
        print("❌ dwara.csv file not found")
        return
    
    print("✅ dwara.csv file found")
    
    try:
        # Read the CSV file
        print("\n📖 Reading CSV file...")
        df = pd.read_csv('dwara.csv', skiprows=2)  # Skip first 2 rows
        
        print(f"✅ CSV loaded successfully")
        print(f"📊 Total rows: {len(df)}")
        print(f"📊 Total columns: {len(df.columns)}")
        
        print(f"\n📋 Column names:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. {col}")
        
        # Show first few rows
        print(f"\n📄 First 3 rows:")
        print(df.head(3).to_string())
        
        # Test search functionality
        print(f"\n🔍 Testing search for 'Sadhguru'...")
        query = 'sadhguru'
        query_lower = query.lower()
        
        # Search in the first column (Folder Name)
        if len(df.columns) > 0:
            folder_name_col = df.columns[0]  # First column
            print(f"🔍 Searching in column: '{folder_name_col}'")
            
            # Filter rows where the folder name contains the query
            mask = df[folder_name_col].astype(str).str.lower().str.contains(query_lower, na=False, regex=False)
            matching_rows = df[mask]
            
            print(f"✅ Found {len(matching_rows)} matches for '{query}'")
            
            if len(matching_rows) > 0:
                print(f"\n📋 First 3 matching results:")
                for i, (_, row) in enumerate(matching_rows.head(3).iterrows()):
                    print(f"\n  Result {i+1}:")
                    print(f"    Filename: {row[folder_name_col]}")
                    print(f"    Category: {row.get('Category', 'N/A')}")
                    print(f"    Size: {row.get('Size', 'N/A')}")
                    print(f"    Status: {row.get('Status', 'N/A')}")
            else:
                print(f"❌ No matches found for '{query}'")
                
                # Try a different search term
                print(f"\n🔍 Testing search for 'Music'...")
                query2 = 'music'
                query2_lower = query2.lower()
                mask2 = df[folder_name_col].astype(str).str.lower().str.contains(query2_lower, na=False, regex=False)
                matching_rows2 = df[mask2]
                
                print(f"✅ Found {len(matching_rows2)} matches for '{query2}'")
                
                if len(matching_rows2) > 0:
                    print(f"\n📋 First 3 matching results:")
                    for i, (_, row) in enumerate(matching_rows2.head(3).iterrows()):
                        print(f"\n  Result {i+1}:")
                        print(f"    Filename: {row[folder_name_col]}")
                        print(f"    Category: {row.get('Category', 'N/A')}")
                        print(f"    Size: {row.get('Size', 'N/A')}")
                        print(f"    Status: {row.get('Status', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dwara_search()
