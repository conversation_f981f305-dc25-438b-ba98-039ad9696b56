{% extends "base.html" %}

{% block title %}Admin Panel - Archives Raw Data Finder{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="bi bi-gear"></i> Admin Panel</h1>
                <span class="badge bg-warning text-dark">Admin Access</span>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-primary text-white">
                <div class="card-body">
                    <i class="bi bi-people display-4 mb-2"></i>
                    <h3>{{ stats.total_users }}</h3>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <i class="bi bi-collection display-4 mb-2"></i>
                    <h3>{{ stats.total_sheets }}</h3>
                    <p class="mb-0">Archive Categories</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <i class="bi bi-database display-4 mb-2"></i>
                    <h3>{{ "{:,}".format(stats.total_records) }}</h3>
                    <p class="mb-0">Total Records</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-warning text-dark">
                <div class="card-body">
                    <i class="bi bi-activity display-4 mb-2"></i>
                    <h3>{{ stats.recent_activity|length }}</h3>
                    <p class="mb-0">Recent Activities</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- File Upload -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-cloud-upload"></i> Upload Excel File</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('admin_upload') }}" method="POST" enctype="multipart/form-data" id="uploadForm">
                        <div class="mb-3">
                            <label for="file" class="form-label">Select Excel File</label>
                            <input type="file" class="form-control" id="file" name="file" 
                                   accept=".xlsx,.xls" required>
                            <div class="form-text">
                                Upload a new Excel file to replace the current archive data. 
                                Supported formats: .xlsx, .xls
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmUpload" required>
                                <label class="form-check-label" for="confirmUpload">
                                    I understand this will replace all current data
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning" id="uploadBtn">
                                <i class="bi bi-cloud-upload"></i> Upload & Process
                            </button>
                        </div>
                    </form>
                    
                    <div class="progress mt-3 d-none" id="uploadProgress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> System Status</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <th>Current Data File:</th>
                            <td>
                                {% if stats.last_processed %}
                                    {{ stats.last_processed.split('/')[-1] if '/' in stats.last_processed else stats.last_processed }}
                                {% else %}
                                    <span class="text-muted">No file processed</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Total Categories:</th>
                            <td>{{ stats.total_sheets }}</td>
                        </tr>
                        <tr>
                            <th>Total Records:</th>
                            <td>{{ "{:,}".format(stats.total_records) }}</td>
                        </tr>
                        <tr>
                            <th>System Status:</th>
                            <td><span class="badge bg-success">Online</span></td>
                        </tr>
                        <tr>
                            <th>Memory Usage:</th>
                            <td><span class="badge bg-info">Optimized</span></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-clock-history"></i> Recent User Activity</h5>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshActivity()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    {% if stats.recent_activity %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Details</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in stats.recent_activity[:20] %}
                                <tr>
                                    <td>
                                        <small>{{ activity.timestamp[:19].replace('T', ' ') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ activity.username }}</span>
                                    </td>
                                    <td>
                                        {% if activity.action == 'user_login' %}
                                            <span class="badge bg-success">Login</span>
                                        {% elif activity.action == 'user_logout' %}
                                            <span class="badge bg-info">Logout</span>
                                        {% elif activity.action == 'search' %}
                                            <span class="badge bg-primary">Search</span>
                                        {% elif activity.action == 'file_upload' %}
                                            <span class="badge bg-warning text-dark">Upload</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">{{ activity.action }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ activity.details[:50] }}{% if activity.details|length > 50 %}...{% endif %}</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ activity.ip_address }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-clock-history display-4 text-muted"></i>
                        <p class="lead mt-3">No recent activity</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = uploadProgress.querySelector('.progress-bar');
    
    uploadForm.addEventListener('submit', function(e) {
        // Show progress bar
        uploadProgress.classList.remove('d-none');
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
        
        // Simulate progress (since we can't track real upload progress easily)
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 500);
        
        // The form will submit normally, but we've shown the progress indicator
    });
});

function refreshActivity() {
    location.reload();
}
</script>
{% endblock %}
