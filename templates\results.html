{% extends "base.html" %}

{% block title %}Search Results - Archives Raw Data Finder{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="bi bi-list-ul"></i> Search Results</h1>
                <a href="{{ url_for('search') }}" class="btn btn-outline-primary">
                    <i class="bi bi-search"></i> New Search
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <span id="searchQueryDisplay"></span>
                        <span id="resultCount" class="badge bg-primary ms-2">0</span>
                    </h5>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" id="exportBtn">
                            <i class="bi bi-download"></i> Export CSV
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="noResults" class="text-center py-5">
                        <i class="bi bi-search display-1 text-muted"></i>
                        <p class="lead mt-3">No results to display</p>
                        <a href="{{ url_for('search') }}" class="btn btn-primary mt-3">
                            <i class="bi bi-search"></i> Go to Search
                        </a>
                    </div>
                    
                    <div id="resultsContainer" class="d-none">
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">Filter Results</span>
                                    <input type="text" class="form-control" id="tableFilter" 
                                           placeholder="Type to filter...">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">Category</span>
                                    <select class="form-select" id="categoryFilter">
                                        <option value="">All Categories</option>
                                        <!-- Categories will be added dynamically -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Results Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="resultsTable">
                                <thead>
                                    <tr>
                                        <th data-sort="media_code">Media Code <i class="bi bi-arrow-down-up sort-icon"></i></th>
                                        <th data-sort="program_name">Program Name <i class="bi bi-arrow-down-up sort-icon"></i></th>
                                        <th data-sort="location">Location <i class="bi bi-arrow-down-up sort-icon"></i></th>
                                        <th data-sort="date">Date <i class="bi bi-arrow-down-up sort-icon"></i></th>
                                        <th data-sort="sheet_name">Category <i class="bi bi-arrow-down-up sort-icon"></i></th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Results will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span class="text-muted">Showing <span id="showingCount">0</span> of <span id="totalCount">0</span> results</span>
                            </div>
                            <div>
                                <button id="loadMoreBtn" class="btn btn-outline-primary d-none">
                                    <i class="bi bi-arrow-down"></i> Load More
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Result Details Modal -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Record Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Details will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
.sort-icon {
    cursor: pointer;
    opacity: 0.5;
}

th[data-sort] {
    cursor: pointer;
}

th[data-sort]:hover .sort-icon {
    opacity: 1;
}

.sort-active .sort-icon {
    opacity: 1;
}

.sort-asc .sort-icon::before {
    content: "\F12F";  /* Bootstrap icon for arrow-up */
}

.sort-desc .sort-icon::before {
    content: "\F143";  /* Bootstrap icon for arrow-down */
}
</style>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const query = urlParams.get('q') || '';
    const sheet = urlParams.get('sheet') || '';
    const column = urlParams.get('column') || '';
    
    // Elements
    const searchQueryDisplay = document.getElementById('searchQueryDisplay');
    const resultCount = document.getElementById('resultCount');
    const noResults = document.getElementById('noResults');
    const resultsContainer = document.getElementById('resultsContainer');
    const resultsTable = document.getElementById('resultsTable').querySelector('tbody');
    const categoryFilter = document.getElementById('categoryFilter');
    const tableFilter = document.getElementById('tableFilter');
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    const showingCount = document.getElementById('showingCount');
    const totalCount = document.getElementById('totalCount');
    const exportBtn = document.getElementById('exportBtn');
    
    // Initialize modal
    const resultModal = new bootstrap.Modal(document.getElementById('resultModal'));
    
    // Results data
    let allResults = [];
    let filteredResults = [];
    let displayedResults = [];
    let currentPage = 0;
    const resultsPerPage = 50;
    let categories = new Set();
    let currentSort = { field: 'media_code', order: 'asc' };
    
    // If we have query parameters, perform search
    if (query || sheet) {
        performSearch(query, sheet, column);
    }
    
    function performSearch(query, sheet, column) {
        // Update display
        searchQueryDisplay.innerHTML = query ? 
            `Results for: <strong>"${query}"</strong>` : 
            'All Results';
        
        if (sheet) {
            searchQueryDisplay.innerHTML += ` in <strong>${sheet}</strong>`;
        }
        
        // Perform search via API
        fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: query,
                sheet_filter: sheet,
                column_filter: column,
                sort_by: 'media_code',
                sort_order: 'asc'
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Search error: ' + data.error);
                return;
            }
            
            // Store results
            allResults = data.results;
            filteredResults = [...allResults];
            
            // Update counts
            resultCount.textContent = data.total_count;
            totalCount.textContent = data.total_count;
            
            // Collect categories
            categories = new Set();
            allResults.forEach(result => {
                if (result.sheet_name) {
                    categories.add(result.sheet_name);
                }
            });
            
            // Populate category filter
            categoryFilter.innerHTML = '<option value="">All Categories</option>';
            Array.from(categories).sort().forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categoryFilter.appendChild(option);
            });
            
            // Show/hide containers
            if (allResults.length === 0) {
                resultsContainer.classList.add('d-none');
                noResults.classList.remove('d-none');
            } else {
                resultsContainer.classList.remove('d-none');
                noResults.classList.add('d-none');
                
                // Display results
                displayResults(true);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while searching. Please try again.');
        });
    }
    
    // Display paginated results
    function displayResults(reset = false) {
        if (reset) {
            // Reset pagination
            currentPage = 0;
            resultsTable.innerHTML = '';
            displayedResults = [];
        }
        
        // Calculate slice for current page
        const start = currentPage * resultsPerPage;
        const end = Math.min(start + resultsPerPage, filteredResults.length);
        const pageResults = filteredResults.slice(start, end);
        
        // Add to displayed results
        displayedResults = displayedResults.concat(pageResults);
        
        // Add results to table
        pageResults.forEach(result => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${result.media_code || ''}</td>
                <td>${result.program_name || ''}</td>
                <td>${result.location || ''}</td>
                <td>${result.date || ''}</td>
                <td><span class="badge bg-info">${result.sheet_name || ''}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary view-details" data-index="${displayedResults.indexOf(result)}">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            `;
            resultsTable.appendChild(row);
        });
        
        // Update pagination
        currentPage++;
        showingCount.textContent = displayedResults.length;
        
        // Show/hide load more button
        if (displayedResults.length < filteredResults.length) {
            loadMoreBtn.classList.remove('d-none');
        } else {
            loadMoreBtn.classList.add('d-none');
        }
        
        // Add event listeners to view details buttons
        document.querySelectorAll('.view-details').forEach(button => {
            button.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                showDetails(displayedResults[index]);
            });
        });
    }
    
    // Load more results
    loadMoreBtn.addEventListener('click', () => displayResults(false));
    
    // Filter results
    tableFilter.addEventListener('input', filterResults);
    categoryFilter.addEventListener('change', filterResults);
    
    function filterResults() {
        const filterText = tableFilter.value.toLowerCase();
        const categoryValue = categoryFilter.value;
        
        // Apply filters
        filteredResults = allResults.filter(result => {
            // Category filter
            if (categoryValue && result.sheet_name !== categoryValue) {
                return false;
            }
            
            // Text filter
            if (filterText) {
                return Object.values(result).some(value => 
                    value && value.toString().toLowerCase().includes(filterText)
                );
            }
            
            return true;
        });
        
        // Apply current sort
        sortResults(currentSort.field, currentSort.order, false);
        
        // Reset and display
        resultsTable.innerHTML = '';
        displayedResults = [];
        currentPage = 0;
        totalCount.textContent = filteredResults.length;
        displayResults(false);
    }
    
    // Sorting
    document.querySelectorAll('th[data-sort]').forEach(th => {
        th.addEventListener('click', function() {
            const field = this.getAttribute('data-sort');
            const order = this.classList.contains('sort-asc') ? 'desc' : 'asc';
            
            // Remove sort classes from all headers
            document.querySelectorAll('th[data-sort]').forEach(el => {
                el.classList.remove('sort-active', 'sort-asc', 'sort-desc');
            });
            
            // Add sort classes to current header
            this.classList.add('sort-active', `sort-${order}`);
            
            // Sort results
            sortResults(field, order);
        });
    });
    
    function sortResults(field, order, reset = true) {
        currentSort = { field, order };
        
        // Sort filtered results
        filteredResults.sort((a, b) => {
            const aValue = a[field] || '';
            const bValue = b[field] || '';
            
            if (aValue < bValue) return order === 'asc' ? -1 : 1;
            if (aValue > bValue) return order === 'asc' ? 1 : -1;
            return 0;
        });
        
        if (reset) {
            // Reset and display
            resultsTable.innerHTML = '';
            displayedResults = [];
            currentPage = 0;
            displayResults(false);
        }
    }
    
    // Show details in modal
    function showDetails(result) {
        document.getElementById('modalTitle').textContent = result.media_code || 'Record Details';
        
        let detailsHtml = '<div class="table-responsive"><table class="table table-bordered">';
        
        // Add all properties to the table
        for (const [key, value] of Object.entries(result)) {
            if (value && key !== 'index') {
                detailsHtml += `
                    <tr>
                        <th style="width: 30%">${key.replace(/_/g, ' ').toUpperCase()}</th>
                        <td>${value}</td>
                    </tr>
                `;
            }
        }
        
        detailsHtml += '</table></div>';
        document.getElementById('modalBody').innerHTML = detailsHtml;
        
        resultModal.show();
    }
    
    // Export to CSV
    exportBtn.addEventListener('click', function() {
        if (filteredResults.length === 0) {
            alert('No results to export');
            return;
        }
        
        // Get all keys from the first result
        const keys = Object.keys(filteredResults[0]);
        
        // Create CSV content
        let csvContent = keys.join(',') + '\n';
        
        filteredResults.forEach(result => {
            const row = keys.map(key => {
                // Escape quotes and wrap in quotes if needed
                const value = result[key] || '';
                const escaped = value.toString().replace(/"/g, '""');
                return `"${escaped}"`;
            });
            csvContent += row.join(',') + '\n';
        });
        
        // Create download link
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `search_results_${new Date().toISOString().slice(0,10)}.csv`);
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
});
</script>
{% endblock %}
