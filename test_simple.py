import pandas as pd

# Test reading the CSV
try:
    print("Reading dwara.csv...")
    df = pd.read_csv('dwara.csv', skiprows=2)
    print(f"Success! Rows: {len(df)}, Columns: {len(df.columns)}")
    print(f"Columns: {list(df.columns)}")
    
    if len(df) > 0:
        print(f"First row data:")
        first_row = df.iloc[0]
        for col in df.columns:
            print(f"  {col}: {first_row[col]}")
            
        # Test search
        query = "music"
        col_name = df.columns[0]
        mask = df[col_name].astype(str).str.lower().str.contains(query, na=False, regex=False)
        results = df[mask]
        print(f"\nSearch results for '{query}': {len(results)} matches")
        
        if len(results) > 0:
            print(f"First match: {results.iloc[0][col_name]}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
