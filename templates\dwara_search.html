{% extends "base.html" %}

{% block title %}Search in Dwara - Archives Raw Data Finder{% endblock %}

{% block content %}
<div class="search-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="mb-3">
                    <i class="bi bi-folder-symlink"></i> Search in Dwara
                </h1>
                <p class="lead mb-4">
                    Search for files and folders in the Dwara archive system
                </p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Search Form -->
        <div class="col-lg-4 mb-4">
            <div class="card sticky-top" style="top: 20px; z-index: 100;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-search"></i> Search Dwara Files</h5>
                </div>
                <div class="card-body">
                    <form id="dwaraSearchForm">
                        <div class="mb-3">
                            <label for="searchQuery" class="form-label">Search Query</label>
                            <input type="text" class="form-control" id="searchQuery" 
                                   placeholder="Enter filename or part of filename..." required>
                            <div class="form-text">
                                <small><strong>Examples:</strong></small><br>
                                <small>• "Sadhguru" - finds all files containing "Sadhguru"</small><br>
                                <small>• "V52307" - finds specific file ID</small><br>
                                <small>• "Music-Video" - finds all music video files</small><br>
                                <small>• "4K" - finds all 4K resolution files</small>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> Search Dwara
                            </button>
                        </div>
                    </form>
                    
                    <!-- Search Info -->
                    <div class="alert alert-info mt-3">
                        <h6><i class="bi bi-info-circle"></i> Search Information</h6>
                        <ul class="mb-0 small">
                            <li><strong>Partial Matching:</strong> Search finds files containing your query</li>
                            <li><strong>Case Insensitive:</strong> "sadhguru" matches "Sadhguru"</li>
                            <li><strong>Real-time Results:</strong> Instant search as you type</li>
                            <li><strong>Complete Data:</strong> Shows all file details from Dwara system</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Results Area -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> Search Results</h5>
                    <div>
                        <span id="resultCount" class="badge bg-primary">0</span>
                        <div class="spinner-border spinner-border-sm text-primary ms-2 loading" role="status" id="searchSpinner">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="noResults" class="text-center py-5">
                        <i class="bi bi-folder2-open display-1 text-muted"></i>
                        <p class="lead mt-3">Enter a search query to find files in Dwara archive</p>
                        <p class="text-muted">Search through thousands of archived files and folders</p>
                    </div>
                    
                    <div id="resultsContainer" class="d-none">
                        <!-- Filter Controls -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">Filter Results</span>
                                    <input type="text" class="form-control" id="tableFilter" 
                                           placeholder="Type to filter results...">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">Category</span>
                                    <select class="form-select" id="categoryFilter">
                                        <option value="">All Categories</option>
                                        <option value="video">Video</option>
                                        <option value="audio">Audio</option>
                                        <option value="image">Image</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Results List -->
                        <div id="resultsList">
                            <!-- Results will be inserted here -->
                        </div>
                        
                        <!-- Load More Button -->
                        <div class="text-center mt-3">
                            <button id="loadMoreBtn" class="btn btn-outline-primary d-none">
                                <i class="bi bi-arrow-down"></i> Load More Results
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Details Modal -->
<div class="modal fade" id="fileModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">File Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Details will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.filename-container {
    border-left: 4px solid #0d6efd;
    transition: all 0.3s ease;
    background-color: #f8f9fa !important;
}

.filename-container:hover {
    background-color: #e3f2fd !important;
    border-left-color: #0056b3;
}

.filename-display {
    background-color: white !important;
    border: 1px solid #dee2e6 !important;
    font-family: 'Courier New', monospace !important;
    font-size: 0.95rem !important;
    line-height: 1.4 !important;
    color: #212529 !important;
    padding: 8px 12px !important;
    word-break: break-all !important;
    white-space: pre-wrap !important;
    max-height: 120px;
    overflow-y: auto;
}

.filename-display:hover {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.copy-btn {
    transition: all 0.3s ease;
}

.copy-btn:hover {
    transform: scale(1.05);
}

.result-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Global copy function
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // Use the modern clipboard API
        navigator.clipboard.writeText(text).then(function() {
            showCopySuccess();
        }).catch(function(err) {
            console.error('Failed to copy: ', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyTextToClipboard(text);
    }
}

// Fallback copy function
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess();
        } else {
            showCopyError();
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showCopyError();
    }

    document.body.removeChild(textArea);
}

// Show copy success message
function showCopySuccess() {
    // Create a temporary toast message
    const toast = document.createElement('div');
    toast.className = 'alert alert-success position-fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = '<i class="bi bi-check-circle"></i> Filename copied to clipboard!';

    document.body.appendChild(toast);

    // Remove the toast after 3 seconds
    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 3000);
}

// Show copy error message
function showCopyError() {
    alert('Failed to copy filename. Please select and copy manually.');
}

document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('dwaraSearchForm');
    const searchQuery = document.getElementById('searchQuery');
    const resultsContainer = document.getElementById('resultsContainer');
    const noResults = document.getElementById('noResults');
    const resultsList = document.getElementById('resultsList');
    const resultCount = document.getElementById('resultCount');
    const searchSpinner = document.getElementById('searchSpinner');
    const tableFilter = document.getElementById('tableFilter');
    const categoryFilter = document.getElementById('categoryFilter');
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    
    // Initialize modal
    const fileModal = new bootstrap.Modal(document.getElementById('fileModal'));
    
    // Current results and pagination
    let allResults = [];
    let filteredResults = [];
    let displayedResults = [];
    let currentPage = 0;
    const resultsPerPage = 20;
    
    // Search form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!searchQuery.value.trim()) {
            alert('Please enter a search query');
            return;
        }
        
        performSearch();
    });
    
    // Real-time search as user types (with debounce)
    let searchTimeout;
    searchQuery.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        if (this.value.trim().length >= 2) {
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 500);
        } else if (this.value.trim().length === 0) {
            // Clear results when search is empty
            allResults = [];
            filteredResults = [];
            displayedResults = [];
            resultsContainer.classList.add('d-none');
            noResults.classList.remove('d-none');
            resultCount.textContent = '0';
        }
    });
    
    function performSearch() {
        const query = searchQuery.value.trim();
        
        if (!query) return;
        
        // Reset pagination
        currentPage = 0;
        allResults = [];
        displayedResults = [];
        
        // Show loading spinner
        searchSpinner.classList.remove('d-none');
        searchSpinner.classList.add('d-inline-block');
        
        // Perform search
        fetch('/api/dwara-search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: query
            }),
        })
        .then(response => response.json())
        .then(data => {
            // Hide loading spinner
            searchSpinner.classList.add('d-none');
            searchSpinner.classList.remove('d-inline-block');
            
            if (data.error) {
                alert('Search error: ' + data.error);
                return;
            }
            
            // Store all results
            allResults = data.results;
            filteredResults = [...allResults];
            
            // Update result count
            resultCount.textContent = data.total_count;
            
            // Show/hide containers
            if (allResults.length === 0) {
                resultsContainer.classList.add('d-none');
                noResults.classList.remove('d-none');
                noResults.innerHTML = `
                    <i class="bi bi-search display-1 text-muted"></i>
                    <p class="lead mt-3">No files found for "${data.query}"</p>
                    <p class="text-muted">Try a different search term or check your spelling</p>
                `;
            } else {
                resultsContainer.classList.remove('d-none');
                noResults.classList.add('d-none');
                
                // Display first page of results
                displayResults(true);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            searchSpinner.classList.add('d-none');
            searchSpinner.classList.remove('d-inline-block');
            alert('An error occurred while searching. Please try again.');
        });
    }
    
    // Display paginated results
    function displayResults(reset = false) {
        if (reset) {
            // Reset pagination
            currentPage = 0;
            resultsList.innerHTML = '';
            displayedResults = [];
        }
        
        // Calculate slice for current page
        const start = currentPage * resultsPerPage;
        const end = Math.min(start + resultsPerPage, filteredResults.length);
        const pageResults = filteredResults.slice(start, end);
        
        // Add to displayed results
        displayedResults = displayedResults.concat(pageResults);
        
        // Add results to list
        pageResults.forEach((result, index) => {
            const resultCard = createResultCard(result, start + index);
            resultsList.appendChild(resultCard);
        });
        
        // Update pagination
        currentPage++;
        
        // Show/hide load more button
        if (displayedResults.length < filteredResults.length) {
            loadMoreBtn.classList.remove('d-none');
        } else {
            loadMoreBtn.classList.add('d-none');
        }
    }
    
    // Create result card
    function createResultCard(result, index) {
        const card = document.createElement('div');
        card.className = 'card mb-3 result-card';

        // Get the folder name (first column) - this is the complete filename
        const folderName = Object.values(result)[0] || 'Unknown';
        const category = result['Category'] || 'Unknown';
        const access = result['Access'] || 'Unknown';
        const size = result['Size'] || 'Unknown';
        const status = result['Status'] || 'Unknown';
        const writtenBy = result['Written By'] || 'Unknown';
        const requestedDate = result['Requested Date'] || 'Unknown';

        card.innerHTML = `
            <div class="card-body">
                <div class="row">
                    <div class="col-md-10">
                        <div class="filename-container mb-3 p-3 bg-light rounded border">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1 me-2">
                                    <label class="form-label mb-1 text-muted small">
                                        <i class="bi bi-file-earmark me-1"></i>Complete Filename:
                                    </label>
                                    <div class="filename-display p-2 bg-white border rounded" style="font-family: 'Courier New', monospace; font-size: 0.95rem; word-break: break-all; line-height: 1.4; color: #0d6efd; min-height: 40px;">
                                        ${folderName}
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-primary copy-btn" type="button" onclick="copyToClipboard('${folderName.replace(/'/g, "\\'")}'); event.stopPropagation();" title="Copy filename">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <small class="text-muted">Category:</small> <span class="badge bg-info">${category}</span><br>
                                <small class="text-muted">Access:</small> <span class="badge bg-${access === 'pub' ? 'success' : 'warning'}">${access}</span><br>
                                <small class="text-muted">Size:</small> <strong>${size}</strong>
                            </div>
                            <div class="col-sm-6">
                                <small class="text-muted">Status:</small> <span class="badge bg-${status === 'completed' ? 'success' : 'secondary'}">${status}</span><br>
                                <small class="text-muted">Written By:</small> <strong>${writtenBy}</strong><br>
                                <small class="text-muted">Requested:</small> <strong>${requestedDate}</strong>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 text-end">
                        <button class="btn btn-outline-primary btn-sm view-details" data-index="${index}">
                            <i class="bi bi-eye"></i> All Details
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Add click event to view details button
        card.querySelector('.view-details').addEventListener('click', function() {
            showFileDetails(result);
        });

        return card;
    }
    
    // Show file details in modal
    function showFileDetails(result) {
        const folderName = Object.values(result)[0] || 'Unknown';
        document.getElementById('modalTitle').textContent = folderName;
        
        let detailsHtml = '<div class="table-responsive"><table class="table table-bordered">';
        
        // Add all properties to the table
        for (const [key, value] of Object.entries(result)) {
            if (value) {
                detailsHtml += `
                    <tr>
                        <th style="width: 30%">${key}</th>
                        <td>${value}</td>
                    </tr>
                `;
            }
        }
        
        detailsHtml += '</table></div>';
        document.getElementById('modalBody').innerHTML = detailsHtml;
        
        fileModal.show();
    }
    
    // Load more results
    loadMoreBtn.addEventListener('click', () => displayResults(false));
    
    // Filter results
    tableFilter.addEventListener('input', filterResults);
    categoryFilter.addEventListener('change', filterResults);
    
    function filterResults() {
        const filterText = tableFilter.value.toLowerCase();
        const categoryValue = categoryFilter.value;
        
        // Apply filters
        filteredResults = allResults.filter(result => {
            // Category filter
            if (categoryValue && result['Category'] !== categoryValue) {
                return false;
            }
            
            // Text filter
            if (filterText) {
                return Object.values(result).some(value => 
                    value && value.toString().toLowerCase().includes(filterText)
                );
            }
            
            return true;
        });
        
        // Reset and display
        resultsList.innerHTML = '';
        displayedResults = [];
        currentPage = 0;
        resultCount.textContent = filteredResults.length;
        displayResults(false);
    }
});
</script>
{% endblock %}
