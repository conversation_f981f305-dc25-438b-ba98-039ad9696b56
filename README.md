# Archives Raw Data Finder

A comprehensive, production-ready Flask web application for searching and analyzing Excel data across multiple sheets with user authentication and admin features.

## Features

- **Multi-User Authentication System**
  - Secure login/registration with password hashing
  - Session management
  - Admin role with special privileges

- **Excel Data Processing**
  - Automatic sheet detection and extraction
  - Column header standardization
  - CSV conversion for fast searching
  - Master CSV creation with sheet origin tracking

- **Advanced Search Functionality**
  - Case-insensitive searching
  - Partial matching
  - Filtering by sheet and column
  - Sorting capabilities
  - Fast performance with in-memory caching

- **Admin Features**
  - Excel file upload and processing
  - User activity logging
  - System statistics

- **Responsive UI**
  - Bootstrap-based responsive design
  - Mobile-friendly interface
  - Interactive data tables

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd archives-raw-data-finder
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Run the application:
   ```
   python app.py
   ```

5. Access the application at http://localhost:5006

## Default Admin Credentials

- Username: `admin`
- Password: `admin123`

**Important:** Change these credentials after first login for security.

## Directory Structure

- `app.py` - Main Flask application
- `data/` - Processed CSV files
- `uploads/` - Uploaded Excel files
- `templates/` - HTML templates
- `static/` - CSS, JS, and other static files
- `users.csv` - User credentials storage
- `logs.csv` - User activity logs

## Usage

1. **Login/Register**
   - Use the login page to access the system
   - New users can register from the registration page

2. **Search Data**
   - Use the search page to find records
   - Filter by category, column
   - Sort results by any field
   - View detailed information for any record

3. **Admin Functions**
   - Upload new Excel files
   - Monitor user activity
   - View system statistics

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Flask - Web framework
- Bootstrap - Frontend framework
- Pandas - Data processing
