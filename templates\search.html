{% extends "base.html" %}

{% block title %}Search - Archives Raw Data Finder{% endblock %}

{% block content %}
<div class="search-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="mb-3">
                    <i class="bi bi-search"></i> Search Archives
                </h1>
                <p class="lead mb-4">
                    Search across all archive categories or filter by specific criteria
                </p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Search Form -->
        <div class="col-lg-4 mb-4">
            <div class="card sticky-top" style="top: 20px; z-index: 100;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-filter"></i> Search & Filters</h5>
                </div>
                <div class="card-body">
                    <form id="searchForm">
                        <div class="mb-3">
                            <label for="searchQuery" class="form-label">Search Query</label>
                            <input type="text" class="form-control" id="searchQuery" 
                                   placeholder="Enter keywords..." required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="sheetFilter" class="form-label">Archive Category</label>
                            <select class="form-select" id="sheetFilter">
                                <option value="">All Categories</option>
                                {% for sheet in sheet_names %}
                                <option value="{{ sheet }}">{{ sheet }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="columnFilter" class="form-label">Column Filter</label>
                            <select class="form-select" id="columnFilter">
                                <option value="">All Columns</option>
                                <option value="media_code">Media Code</option>
                                <option value="program_name">Program Name</option>
                                <option value="location">Location</option>
                                <option value="country">Country</option>
                                <option value="date">Date</option>
                                <option value="notes">Notes</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="sortBy" class="form-label">Sort By</label>
                            <select class="form-select" id="sortBy">
                                <option value="sheet_name">Archive Category</option>
                                <option value="media_code">Media Code</option>
                                <option value="program_name">Program Name</option>
                                <option value="location">Location</option>
                                <option value="date">Date</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="sortOrder" class="form-label">Sort Order</label>
                            <select class="form-select" id="sortOrder">
                                <option value="asc">Ascending</option>
                                <option value="desc">Descending</option>
                            </select>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Results Area -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> Search Results</h5>
                    <div>
                        <span id="resultCount" class="badge bg-primary">0</span>
                        <div class="spinner-border spinner-border-sm text-primary ms-2 loading" role="status" id="searchSpinner">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="noResults" class="text-center py-5">
                        <i class="bi bi-search display-1 text-muted"></i>
                        <p class="lead mt-3">Enter a search query to find archive data</p>
                    </div>
                    
                    <div id="resultsContainer" class="d-none">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Media Code</th>
                                        <th>Program Name</th>
                                        <th>Location</th>
                                        <th>Date</th>
                                        <th>Category</th>
                                    </tr>
                                </thead>
                                <tbody id="resultsTable">
                                    <!-- Results will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center mt-3">
                            <button id="loadMoreBtn" class="btn btn-outline-primary d-none">
                                <i class="bi bi-arrow-down"></i> Load More Results
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Result Details Modal -->
            <div class="modal fade" id="resultModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="modalTitle">Record Details</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="modalBody">
                            <!-- Details will be inserted here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    const searchQuery = document.getElementById('searchQuery');
    const sheetFilter = document.getElementById('sheetFilter');
    const columnFilter = document.getElementById('columnFilter');
    const sortBy = document.getElementById('sortBy');
    const sortOrder = document.getElementById('sortOrder');
    const resultsContainer = document.getElementById('resultsContainer');
    const noResults = document.getElementById('noResults');
    const resultsTable = document.getElementById('resultsTable');
    const resultCount = document.getElementById('resultCount');
    const searchSpinner = document.getElementById('searchSpinner');
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    
    // Get URL parameters for pre-filling the form
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('q')) {
        searchQuery.value = urlParams.get('q');
    }
    if (urlParams.has('sheet')) {
        sheetFilter.value = urlParams.get('sheet');
    }
    
    // Initialize modal
    const resultModal = new bootstrap.Modal(document.getElementById('resultModal'));
    
    // Current results and pagination
    let allResults = [];
    let currentPage = 0;
    const resultsPerPage = 50;
    
    // Search form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!searchQuery.value.trim()) {
            alert('Please enter a search query');
            return;
        }
        
        // Reset pagination
        currentPage = 0;
        allResults = [];
        
        // Show loading spinner
        searchSpinner.classList.remove('d-none');
        searchSpinner.classList.add('d-inline-block');
        
        // Perform search
        fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: searchQuery.value.trim(),
                sheet_filter: sheetFilter.value,
                column_filter: columnFilter.value,
                sort_by: sortBy.value,
                sort_order: sortOrder.value
            }),
        })
        .then(response => response.json())
        .then(data => {
            // Hide loading spinner
            searchSpinner.classList.add('d-none');
            searchSpinner.classList.remove('d-inline-block');
            
            if (data.error) {
                alert('Search error: ' + data.error);
                return;
            }
            
            // Store all results
            allResults = data.results;
            
            // Update result count
            resultCount.textContent = data.total_count;
            
            // Show/hide containers
            if (allResults.length === 0) {
                resultsContainer.classList.add('d-none');
                noResults.classList.remove('d-none');
                noResults.innerHTML = `
                    <i class="bi bi-search display-1 text-muted"></i>
                    <p class="lead mt-3">No results found for "${data.query}"</p>
                `;
            } else {
                resultsContainer.classList.remove('d-none');
                noResults.classList.add('d-none');
                
                // Display first page of results
                displayResults();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            searchSpinner.classList.add('d-none');
            searchSpinner.classList.remove('d-inline-block');
            alert('An error occurred while searching. Please try again.');
        });
    });
    
    // Display paginated results
    function displayResults() {
        // Clear existing results
        if (currentPage === 0) {
            resultsTable.innerHTML = '';
        }
        
        // Calculate slice for current page
        const start = currentPage * resultsPerPage;
        const end = Math.min(start + resultsPerPage, allResults.length);
        const pageResults = allResults.slice(start, end);
        
        // Add results to table
        pageResults.forEach(result => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${result.media_code || ''}</td>
                <td>${result.program_name || ''}</td>
                <td>${result.location || ''}</td>
                <td>${result.date || ''}</td>
                <td><span class="badge bg-info">${result.sheet_name || ''}</span></td>
            `;
            
            // Add click event to show details
            row.style.cursor = 'pointer';
            row.addEventListener('click', () => showDetails(result));
            
            resultsTable.appendChild(row);
        });
        
        // Update pagination
        currentPage++;
        
        // Show/hide load more button
        if (end < allResults.length) {
            loadMoreBtn.classList.remove('d-none');
        } else {
            loadMoreBtn.classList.add('d-none');
        }
    }
    
    // Load more results
    loadMoreBtn.addEventListener('click', displayResults);
    
    // Show details in modal
    function showDetails(result) {
        document.getElementById('modalTitle').textContent = result.media_code || 'Record Details';
        
        let detailsHtml = '<div class="table-responsive"><table class="table table-bordered">';
        
        // Add all properties to the table
        for (const [key, value] of Object.entries(result)) {
            if (value && key !== 'index') {
                detailsHtml += `
                    <tr>
                        <th style="width: 30%">${key.replace(/_/g, ' ').toUpperCase()}</th>
                        <td>${value}</td>
                    </tr>
                `;
            }
        }
        
        detailsHtml += '</table></div>';
        document.getElementById('modalBody').innerHTML = detailsHtml;
        
        resultModal.show();
    }
    
    // Auto-search if query parameter is present
    if (searchQuery.value) {
        searchForm.dispatchEvent(new Event('submit'));
    }
});
</script>
{% endblock %}
