{% extends "base.html" %}

{% block title %}Search - Archives Raw Data Finder{% endblock %}

{% block content %}
<div class="search-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="mb-3">
                    <i class="bi bi-search"></i> Search Archives
                </h1>
                <p class="lead mb-4">
                    Search across all archive categories or filter by specific criteria
                </p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Search Form -->
        <div class="col-lg-4 mb-4">
            <div class="card sticky-top" style="top: 20px; z-index: 100;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-filter"></i> Search & Filters</h5>
                </div>
                <div class="card-body">
                    <form id="searchForm">
                        <!-- Smart Search Info -->
                        <div class="alert alert-info mb-3">
                            <h6><i class="bi bi-lightbulb"></i> Smart Search Features</h6>
                            <ul class="mb-0 small">
                                <li><strong>Date Intelligence:</strong> Searches "03-Apr-2009" and finds "Apr 3, 09" format (±9 days)</li>
                                <li><strong>Audio Code Ranges:</strong> Searches "H60" and finds "MD: H59 to H65" or "H57, H58, H59"</li>
                                <li><strong>Multi-Column Detection:</strong> Automatically searches relevant columns (D, E, H, I)</li>
                                <li><strong>Format Flexibility:</strong> Handles various date and audio code formats</li>
                            </ul>
                        </div>

                        <!-- Basic Search -->
                        <div class="mb-3">
                            <label for="searchQuery" class="form-label">Search Query</label>
                            <input type="text" class="form-control" id="searchQuery"
                                   placeholder="Enter keywords, dates, or audio codes..." required>
                            <div class="form-text">
                                <small><strong>Examples:</strong></small><br>
                                <small><strong>Text:</strong> "Hindi", "YouTube Hindi Mahashivratri"</small><br>
                                <small><strong>Dates:</strong> "03-Apr-2009" (finds ±9 days, handles formats like "Apr 3, 09")</small><br>
                                <small><strong>Audio Codes:</strong> "H59" (finds in ranges like "H59 to H65" or lists like "H57, H58, H59")</small>
                            </div>
                        </div>

                        <!-- Advanced Search Options -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#advancedOptions" aria-expanded="false">
                                        <i class="bi bi-gear"></i> Advanced Search Options
                                    </button>
                                </h6>
                            </div>
                            <div class="collapse" id="advancedOptions">
                                <div class="card-body">
                                    <!-- Match Type -->
                                    <div class="mb-3">
                                        <label class="form-label">Match Type</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="matchType"
                                                   id="partialMatch" value="partial" checked>
                                            <label class="form-check-label" for="partialMatch">
                                                Partial Match (e.g., "Hin" matches "Hindi")
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="matchType"
                                                   id="exactMatch" value="exact">
                                            <label class="form-check-label" for="exactMatch">
                                                Exact Match (e.g., "Hindi" only matches "Hindi")
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Multi-keyword Search -->
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="multiKeywords">
                                            <label class="form-check-label" for="multiKeywords">
                                                Multi-keyword Search (all words must be found)
                                            </label>
                                        </div>
                                        <div class="form-text">
                                            <small>Example: "YouTube Hindi Mahashivratri" will find rows containing all three words</small>
                                        </div>
                                    </div>

                                    <!-- Date Search -->
                                    <div class="mb-3">
                                        <label for="dateSearch" class="form-label">Date Search (Optional)</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="dateSearch"
                                                   placeholder="e.g., 03-Apr-2009, Apr 3, 09, 2009-04-03">
                                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-calendar"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li><a class="dropdown-item" href="#" onclick="setDateFilter('today'); return false;">Today</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setDateFilter('yesterday'); return false;">Yesterday</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setDateFilter('last7days'); return false;">Last 7 days</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setDateFilter('last30days'); return false;">Last 30 days</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setDateFilter('thisMonth'); return false;">This month</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setDateFilter('lastMonth'); return false;">Last month</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="setDateFilter('thisYear'); return false;">This year</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" onclick="setDateFilter('clear'); return false;">Clear date filter</a></li>
                                            </ul>
                                        </div>
                                        <div class="form-text">
                                            <small>Finds dates within ±9 days of the specified date</small>
                                        </div>
                                    </div>

                                    <!-- Date Range -->
                                    <div class="mb-3">
                                        <label for="dateRange" class="form-label">Date Range (Days)</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="dateRange"
                                                   value="9" min="0" max="365">
                                            <button class="btn btn-outline-secondary" type="button"
                                                    id="dateRangeInfo" data-bs-toggle="tooltip"
                                                    title="Number of days before/after the date to include">
                                                <i class="bi bi-info-circle"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">
                                            <small>Number of days before/after the date to include</small>
                                        </div>
                                    </div>

                                    <!-- Date Range Picker (Optional) -->
                                    <div class="mb-3">
                                        <label class="form-label">Date Range Picker (Optional)</label>
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <input type="date" class="form-control" id="startDate"
                                                       placeholder="Start Date">
                                            </div>
                                            <div class="col-6">
                                                <input type="date" class="form-control" id="endDate"
                                                       placeholder="End Date">
                                            </div>
                                        </div>
                                        <div class="form-text">
                                            <small>Select a date range to search between specific dates</small>
                                        </div>
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="useDateRange">
                                            <label class="form-check-label" for="useDateRange">
                                                Use date range instead of single date
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="mb-3">
                            <label for="sheetFilter" class="form-label">Archive Category</label>
                            <select class="form-select" id="sheetFilter">
                                <option value="">All Categories</option>
                                {% for sheet in sheet_names %}
                                <option value="{{ sheet }}">{{ sheet }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="columnFilter" class="form-label">Column Filter</label>
                            <select class="form-select" id="columnFilter">
                                <option value="">All Columns</option>
                                <option value="media_code">Media Code</option>
                                <option value="program_name">Program Name</option>
                                <option value="location">Location</option>
                                <option value="country">Country</option>
                                <option value="date">Date</option>
                                <option value="audio">Audio (with smart normalization)</option>
                                <option value="notes">Notes</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="sortBy" class="form-label">Sort By</label>
                            <select class="form-select" id="sortBy">
                                <option value="sheet_name">Archive Category</option>
                                <option value="media_code">Media Code</option>
                                <option value="program_name">Program Name</option>
                                <option value="location">Location</option>
                                <option value="date">Date</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="sortOrder" class="form-label">Sort Order</label>
                            <select class="form-select" id="sortOrder">
                                <option value="asc">Ascending</option>
                                <option value="desc">Descending</option>
                            </select>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Results Area -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> Search Results</h5>
                    <div>
                        <span id="resultCount" class="badge bg-primary">0</span>
                        <div class="spinner-border spinner-border-sm text-primary ms-2 loading" role="status" id="searchSpinner">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="noResults" class="text-center py-5">
                        <i class="bi bi-search display-1 text-muted"></i>
                        <p class="lead mt-3">Enter a search query to find archive data</p>
                    </div>
                    
                    <div id="resultsContainer" class="d-none">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Media Code</th>
                                        <th>Program Name</th>
                                        <th>Location</th>
                                        <th>Date</th>
                                        <th>Category</th>
                                    </tr>
                                </thead>
                                <tbody id="resultsTable">
                                    <!-- Results will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center mt-3">
                            <button id="loadMoreBtn" class="btn btn-outline-primary d-none">
                                <i class="bi bi-arrow-down"></i> Load More Results
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Result Details Modal -->
            <div class="modal fade" id="resultModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="modalTitle">Record Details</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="modalBody">
                            <!-- Details will be inserted here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Function to set date filter based on predefined options
function setDateFilter(option) {
    const dateSearch = document.getElementById('dateSearch');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const useDateRange = document.getElementById('useDateRange');

    const today = new Date();
    const formatDate = (date) => {
        const day = date.getDate();
        const month = date.toLocaleString('default', { month: 'short' });
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    };

    switch (option) {
        case 'today':
            dateSearch.value = formatDate(today);
            break;
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            dateSearch.value = formatDate(yesterday);
            break;
        case 'last7days':
            const sevenDaysAgo = new Date(today);
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            startDate.valueAsDate = sevenDaysAgo;
            endDate.valueAsDate = today;
            useDateRange.checked = true;
            break;
        case 'last30days':
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            startDate.valueAsDate = thirtyDaysAgo;
            endDate.valueAsDate = today;
            useDateRange.checked = true;
            break;
        case 'thisMonth':
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            startDate.valueAsDate = firstDayOfMonth;
            endDate.valueAsDate = today;
            useDateRange.checked = true;
            break;
        case 'lastMonth':
            const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
            startDate.valueAsDate = firstDayOfLastMonth;
            endDate.valueAsDate = lastDayOfLastMonth;
            useDateRange.checked = true;
            break;
        case 'thisYear':
            const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
            startDate.valueAsDate = firstDayOfYear;
            endDate.valueAsDate = today;
            useDateRange.checked = true;
            break;
        case 'clear':
            dateSearch.value = '';
            startDate.value = '';
            endDate.value = '';
            useDateRange.checked = false;
            break;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    const searchQuery = document.getElementById('searchQuery');
    const sheetFilter = document.getElementById('sheetFilter');
    const columnFilter = document.getElementById('columnFilter');
    const sortBy = document.getElementById('sortBy');
    const sortOrder = document.getElementById('sortOrder');
    const resultsContainer = document.getElementById('resultsContainer');
    const noResults = document.getElementById('noResults');
    const resultsTable = document.getElementById('resultsTable');
    const resultCount = document.getElementById('resultCount');
    const searchSpinner = document.getElementById('searchSpinner');
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    const dateSearch = document.getElementById('dateSearch');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const useDateRange = document.getElementById('useDateRange');

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle date range checkbox
    useDateRange.addEventListener('change', function() {
        if (this.checked) {
            dateSearch.disabled = true;
            startDate.disabled = false;
            endDate.disabled = false;
        } else {
            dateSearch.disabled = false;
            startDate.disabled = true;
            endDate.disabled = true;
        }
    });

    // Initialize date range fields
    startDate.disabled = true;
    endDate.disabled = true;

    // Get URL parameters for pre-filling the form
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('q')) {
        searchQuery.value = urlParams.get('q');
    }
    if (urlParams.has('sheet')) {
        sheetFilter.value = urlParams.get('sheet');
    }

    // Initialize modal
    const resultModal = new bootstrap.Modal(document.getElementById('resultModal'));

    // Current results and pagination
    let allResults = [];
    let currentPage = 0;
    const resultsPerPage = 50;
    
    // Search form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!searchQuery.value.trim()) {
            alert('Please enter a search query');
            return;
        }
        
        // Reset pagination
        currentPage = 0;
        allResults = [];
        
        // Show loading spinner
        searchSpinner.classList.remove('d-none');
        searchSpinner.classList.add('d-inline-block');
        
        // Get advanced search options
        const matchType = document.querySelector('input[name="matchType"]:checked').value;
        const multiKeywords = document.getElementById('multiKeywords').checked;
        let dateSearchValue = '';
        const dateRange = parseInt(document.getElementById('dateRange').value) || 9;

        // Handle date search vs date range
        if (useDateRange.checked && startDate.value && endDate.value) {
            // Use date range
            dateSearchValue = `${startDate.value} to ${endDate.value}`;
        } else if (dateSearch.value.trim()) {
            // Use single date search
            dateSearchValue = dateSearch.value.trim();
        }

        // Perform search
        fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: searchQuery.value.trim(),
                sheet_filter: sheetFilter.value,
                column_filter: columnFilter.value,
                sort_by: sortBy.value,
                sort_order: sortOrder.value,
                match_type: matchType,
                multi_keywords: multiKeywords,
                date_search: dateSearchValue || null,
                date_range: dateRange
            }),
        })
        .then(response => response.json())
        .then(data => {
            // Hide loading spinner
            searchSpinner.classList.add('d-none');
            searchSpinner.classList.remove('d-inline-block');
            
            if (data.error) {
                alert('Search error: ' + data.error);
                return;
            }
            
            // Store all results
            allResults = data.results;
            
            // Update result count
            resultCount.textContent = data.total_count;
            
            // Show/hide containers
            if (allResults.length === 0) {
                resultsContainer.classList.add('d-none');
                noResults.classList.remove('d-none');
                noResults.innerHTML = `
                    <i class="bi bi-search display-1 text-muted"></i>
                    <p class="lead mt-3">No results found for "${data.query}"</p>
                `;
            } else {
                resultsContainer.classList.remove('d-none');
                noResults.classList.add('d-none');
                
                // Display first page of results
                displayResults();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            searchSpinner.classList.add('d-none');
            searchSpinner.classList.remove('d-inline-block');
            alert('An error occurred while searching. Please try again.');
        });
    });
    
    // Display paginated results
    function displayResults() {
        // Clear existing results
        if (currentPage === 0) {
            resultsTable.innerHTML = '';
        }
        
        // Calculate slice for current page
        const start = currentPage * resultsPerPage;
        const end = Math.min(start + resultsPerPage, allResults.length);
        const pageResults = allResults.slice(start, end);
        
        // Add results to table
        pageResults.forEach(result => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${result.media_code || ''}</td>
                <td>${result.program_name || ''}</td>
                <td>${result.location || ''}</td>
                <td>${result.date || ''}</td>
                <td><span class="badge bg-info">${result.sheet_name || ''}</span></td>
            `;
            
            // Add click event to show details
            row.style.cursor = 'pointer';
            row.addEventListener('click', () => showDetails(result));
            
            resultsTable.appendChild(row);
        });
        
        // Update pagination
        currentPage++;
        
        // Show/hide load more button
        if (end < allResults.length) {
            loadMoreBtn.classList.remove('d-none');
        } else {
            loadMoreBtn.classList.add('d-none');
        }
    }
    
    // Load more results
    loadMoreBtn.addEventListener('click', displayResults);
    
    // Show details in modal
    function showDetails(result) {
        document.getElementById('modalTitle').textContent = result.media_code || 'Record Details';
        
        let detailsHtml = '<div class="table-responsive"><table class="table table-bordered">';
        
        // Add all properties to the table
        for (const [key, value] of Object.entries(result)) {
            if (value && key !== 'index') {
                detailsHtml += `
                    <tr>
                        <th style="width: 30%">${key.replace(/_/g, ' ').toUpperCase()}</th>
                        <td>${value}</td>
                    </tr>
                `;
            }
        }
        
        detailsHtml += '</table></div>';
        document.getElementById('modalBody').innerHTML = detailsHtml;
        
        resultModal.show();
    }
    
    // Auto-search if query parameter is present
    if (searchQuery.value) {
        searchForm.dispatchEvent(new Event('submit'));
    }
});
</script>
{% endblock %}
