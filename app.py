"""
Archives Raw Data Finder - Flask Web Application
A comprehensive, production-ready Flask app for searching and analyzing Excel data
across multiple sheets with user authentication and admin features.

Author: Augment Agent
Date: 2025-07-21
"""

import os
import csv
import json
import logging
import pandas as pd
import re
from datetime import datetime, timedelta
from functools import wraps
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from dateutil import parser
from difflib import SequenceMatcher

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['DATA_FOLDER'] = 'data'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

# Create necessary directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['DATA_FOLDER'], exist_ok=True)
os.makedirs('templates', exist_ok=True)
os.makedirs('static/css', exist_ok=True)
os.makedirs('static/js', exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global variables for cached data
cached_data = {}
sheet_names = []
last_processed_file = None

# User class for Flask-Login
class User(UserMixin):
    def __init__(self, id, username, email, password_hash, is_admin=False):
        self.id = id
        self.username = username
        self.email = email
        self.password_hash = password_hash
        self.is_admin = is_admin

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    @staticmethod
    def get(user_id):
        """Get user by ID from users.csv"""
        try:
            with open('users.csv', 'r', newline='', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    if row['id'] == str(user_id):
                        return User(
                            id=row['id'],
                            username=row['username'],
                            email=row['email'],
                            password_hash=row['password_hash'],
                            is_admin=row.get('is_admin', 'False') == 'True'
                        )
        except FileNotFoundError:
            pass
        return None

    @staticmethod
    def get_by_username(username):
        """Get user by username from users.csv"""
        try:
            with open('users.csv', 'r', newline='', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    if row['username'] == username:
                        return User(
                            id=row['id'],
                            username=row['username'],
                            email=row['email'],
                            password_hash=row['password_hash'],
                            is_admin=row.get('is_admin', 'False') == 'True'
                        )
        except FileNotFoundError:
            pass
        return None

    def save(self):
        """Save user to users.csv"""
        users = []
        user_exists = False
        
        # Read existing users
        try:
            with open('users.csv', 'r', newline='', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                users = list(reader)
        except FileNotFoundError:
            pass
        
        # Update existing user or add new one
        for i, user in enumerate(users):
            if user['id'] == str(self.id):
                users[i] = {
                    'id': self.id,
                    'username': self.username,
                    'email': self.email,
                    'password_hash': self.password_hash,
                    'is_admin': str(self.is_admin),
                    'created_at': user.get('created_at', datetime.now().isoformat())
                }
                user_exists = True
                break
        
        if not user_exists:
            users.append({
                'id': self.id,
                'username': self.username,
                'email': self.email,
                'password_hash': self.password_hash,
                'is_admin': str(self.is_admin),
                'created_at': datetime.now().isoformat()
            })
        
        # Write back to file
        with open('users.csv', 'w', newline='', encoding='utf-8') as file:
            if users:
                fieldnames = ['id', 'username', 'email', 'password_hash', 'is_admin', 'created_at']
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(users)

@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login"""
    return User.get(user_id)

def admin_required(f):
    """Decorator to require admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('Admin access required.', 'error')
            return redirect(url_for('home'))
        return f(*args, **kwargs)
    return decorated_function

def log_user_activity(action, details=None):
    """Log user activity to logs.csv"""
    try:
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'user_id': current_user.id if current_user.is_authenticated else 'anonymous',
            'username': current_user.username if current_user.is_authenticated else 'anonymous',
            'action': action,
            'details': details or '',
            'ip_address': request.remote_addr
        }
        
        file_exists = os.path.exists('logs.csv')
        with open('logs.csv', 'a', newline='', encoding='utf-8') as file:
            fieldnames = ['timestamp', 'user_id', 'username', 'action', 'details', 'ip_address']
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            if not file_exists:
                writer.writeheader()
            writer.writerow(log_entry)
    except Exception as e:
        logger.error(f"Failed to log user activity: {e}")

# Excel Processing Utilities
def standardize_column_name(col_name):
    """Standardize column names for consistency"""
    if pd.isna(col_name) or str(col_name).startswith('Unnamed'):
        return None

    # Clean and standardize column names
    clean_name = str(col_name).strip().lower()
    clean_name = ''.join(c for c in clean_name if c.isalnum() or c in [' ', '_'])
    clean_name = clean_name.replace(' ', '_')

    # Map common variations to standard names
    column_mapping = {
        'media_code': 'media_code',
        'program_name': 'program_name',
        'subtitle': 'program_name',
        'event_name': 'program_name',
        'place_name': 'program_name',
        'material_content': 'program_name',
        'location': 'location',
        'country': 'country',
        'date': 'date',
        'camera': 'camera',
        'tape': 'tape_number',
        'tape_': 'tape_number',
        'total': 'total',
        'audio': 'audio',
        'notes': 'notes',
        'source': 'source',
        'number': 'number',
        'num': 'number',
        'nubber': 'number',
        'digitizing': 'digitizing',
        'backup': 'backup',
        'label_lto': 'label_lto',
        'dv_dv': 'dv_dv',
        'categorization': 'categorization'
    }

    return column_mapping.get(clean_name, clean_name)

def standardize_date_format(date_str):
    """Convert date string to DD-MMM-YYYY format"""
    if not date_str or not isinstance(date_str, str) or date_str.strip() == '':
        return ''

    date_str = date_str.strip()
    if not date_str:
        return ''

    try:
        # Parse the date using dateutil.parser
        parsed_date = parser.parse(date_str, fuzzy=True)

        # If the year is less than 100, assume it's a 2-digit year
        if parsed_date.year < 100:
            # Assume years 00-30 are 2000-2030, 31-99 are 1931-1999
            if parsed_date.year <= 30:
                parsed_date = parsed_date.replace(year=parsed_date.year + 2000)
            else:
                parsed_date = parsed_date.replace(year=parsed_date.year + 1900)

        # Format as DD-MMM-YYYY
        return parsed_date.strftime('%d-%b-%Y')
    except:
        # If parsing fails, return original string
        return date_str

def identify_date_column(df, sheet_name):
    """Identify which column contains dates in a sheet"""
    date_column = None
    date_column_index = None

    # Check columns D and E first (indices 3 and 4)
    potential_columns = []

    # Add column D and E by index
    if len(df.columns) > 3:
        potential_columns.append((3, df.columns[3]))
    if len(df.columns) > 4:
        potential_columns.append((4, df.columns[4]))

    # Also check columns with date-related names
    for i, col_name in enumerate(df.columns):
        col_lower = str(col_name).lower()
        if ('date' in col_lower or
            'time' in col_lower or
            'day' in col_lower or
            'month' in col_lower or
            'year' in col_lower):
            potential_columns.append((i, col_name))

    # Remove duplicates
    potential_columns = list(set(potential_columns))

    # Test each potential column for date content
    best_score = 0
    best_column = None
    best_index = None

    for col_index, col_name in potential_columns:
        if col_index >= len(df.columns):
            continue

        # Sample some values from the column
        sample_values = df.iloc[:min(50, len(df)), col_index].dropna()
        date_count = 0
        total_count = 0

        for value in sample_values:
            if value and str(value).strip():
                total_count += 1
                # Try to parse as date
                try:
                    parsed = parser.parse(str(value), fuzzy=True)
                    # Check if it looks like a reasonable date
                    if 1900 <= parsed.year <= 2030:
                        date_count += 1
                except:
                    pass

        # Calculate score (percentage of values that are dates)
        if total_count > 0:
            score = date_count / total_count
            if score > best_score and score > 0.3:  # At least 30% should be dates
                best_score = score
                best_column = col_name
                best_index = col_index

    logger.info(f"Sheet '{sheet_name}': Date column identified as '{best_column}' (index {best_index}) with {best_score:.2%} date content")

    return best_column, best_index

def process_excel_file(file_path):
    """Process Excel file and create CSV files for each sheet with date standardization"""
    global cached_data, sheet_names, last_processed_file

    try:
        logger.info(f"Starting to process Excel file: {file_path}")

        # Read Excel file
        xl = pd.ExcelFile(file_path)
        sheet_names = [name for name in xl.sheet_names if name not in ['Sheet2', 'Sheet3']]

        cached_data = {}
        all_data = []
        date_analysis = {}

        for sheet_name in sheet_names:
            logger.info(f"Processing sheet: {sheet_name}")

            # Read sheet
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            if df.empty:
                continue

            # Use first row as headers if current headers are mostly "Unnamed"
            if df.columns[0].startswith('Category') or 'Unnamed' in str(df.columns[1]):
                # First row contains the actual headers
                new_headers = df.iloc[0].tolist()
                df = df.iloc[1:].reset_index(drop=True)
                df.columns = new_headers

            # Identify date column before standardizing column names
            original_date_column, date_column_index = identify_date_column(df, sheet_name)

            # Standardize column names
            standardized_columns = []
            for col in df.columns:
                std_col = standardize_column_name(col)
                if std_col:
                    standardized_columns.append(std_col)
                else:
                    standardized_columns.append(f"column_{len(standardized_columns)}")

            df.columns = standardized_columns

            # Standardize dates in the identified date column
            if date_column_index is not None and date_column_index < len(df.columns):
                date_column_name = df.columns[date_column_index]
                logger.info(f"Standardizing dates in column '{date_column_name}' for sheet '{sheet_name}'")

                # Count original and standardized dates
                original_dates = []
                standardized_dates = []

                for i, value in enumerate(df.iloc[:, date_column_index]):
                    if value and str(value).strip():
                        original_date = str(value).strip()
                        standardized_date = standardize_date_format(original_date)

                        if original_date != standardized_date:
                            original_dates.append(original_date)
                            standardized_dates.append(standardized_date)

                        df.iloc[i, date_column_index] = standardized_date

                # Store analysis info
                date_analysis[sheet_name] = {
                    'column_name': original_date_column,
                    'column_index': date_column_index,
                    'standardized_column_name': date_column_name,
                    'dates_processed': len(original_dates),
                    'sample_conversions': list(zip(original_dates[:5], standardized_dates[:5]))
                }

                logger.info(f"Standardized {len(original_dates)} dates in sheet '{sheet_name}'")
            else:
                date_analysis[sheet_name] = {
                    'column_name': None,
                    'column_index': None,
                    'standardized_column_name': None,
                    'dates_processed': 0,
                    'sample_conversions': []
                }
                logger.info(f"No date column identified in sheet '{sheet_name}'")

            # Clean data
            df = df.fillna('')  # Replace NaN with empty strings
            df = df.astype(str)  # Convert all to strings for consistent searching

            # Add sheet name column
            df['sheet_name'] = sheet_name

            # Save individual CSV
            csv_path = os.path.join(app.config['DATA_FOLDER'], f"{sheet_name.replace('/', '_')}.csv")
            df.to_csv(csv_path, index=False, encoding='utf-8')

            # Cache data for fast searching
            cached_data[sheet_name] = df

            # Add to master data
            all_data.append(df)

            logger.info(f"Processed sheet {sheet_name}: {len(df)} rows")

        # Create master CSV
        if all_data:
            master_df = pd.concat(all_data, ignore_index=True, sort=False)
            master_path = os.path.join(app.config['DATA_FOLDER'], 'master.csv')
            master_df.to_csv(master_path, index=False, encoding='utf-8')
            logger.info(f"Created master CSV with {len(master_df)} total rows")

        # Save date analysis report
        analysis_path = os.path.join(app.config['DATA_FOLDER'], 'date_analysis.json')
        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(date_analysis, f, indent=2, ensure_ascii=False)

        last_processed_file = file_path
        logger.info("Excel processing completed successfully")

        # Create summary of date standardization
        total_dates_processed = sum(info['dates_processed'] for info in date_analysis.values())
        sheets_with_dates = sum(1 for info in date_analysis.values() if info['dates_processed'] > 0)

        summary = f"Successfully processed {len(sheet_names)} sheets with {sum(len(df) for df in cached_data.values())} total records. Date standardization: {total_dates_processed} dates standardized across {sheets_with_dates} sheets."

        return True, summary

    except Exception as e:
        logger.error(f"Error processing Excel file: {e}")
        return False, f"Error processing file: {str(e)}"

def normalize_audio_value(value):
    """Normalize audio values for better matching"""
    if not value or not isinstance(value, str):
        return ""

    # Convert to lowercase
    normalized = value.lower().strip()

    # Map common variations
    audio_mapping = {
        'eng': 'english',
        'english voice over': 'english',
        'eng vo': 'english',
        'hin': 'hindi',
        'hindi voice over': 'hindi',
        'hin vo': 'hindi',
        'tam': 'tamil',
        'tamil voice over': 'tamil',
        'tam vo': 'tamil',
        'tel': 'telugu',
        'telugu voice over': 'telugu',
        'tel vo': 'telugu',
        'kan': 'kannada',
        'kannada voice over': 'kannada',
        'kan vo': 'kannada',
        'mal': 'malayalam',
        'malayalam voice over': 'malayalam',
        'mal vo': 'malayalam',
        'not available': 'na',
        'not avail': 'na',
        'n/a': 'na',
    }

    # Check for matches in the mapping
    for key, mapped_value in audio_mapping.items():
        if key in normalized:
            return mapped_value

    return normalized

def parse_date_string(date_str):
    """Parse date string in various formats"""
    if not date_str or not isinstance(date_str, str):
        return None

    date_str = date_str.strip()
    if not date_str:
        return None

    try:
        # Handle specific formats like "Apr 3, 09"
        # First try to parse as-is
        parsed_date = parser.parse(date_str, fuzzy=True)

        # If the year is less than 100, assume it's a 2-digit year
        if parsed_date.year < 100:
            # Assume years 00-30 are 2000-2030, 31-99 are 1931-1999
            if parsed_date.year <= 30:
                parsed_date = parsed_date.replace(year=parsed_date.year + 2000)
            else:
                parsed_date = parsed_date.replace(year=parsed_date.year + 1900)

        return parsed_date.date()
    except:
        # If parsing fails, return None
        return None

def extract_audio_codes(text):
    """Extract audio codes from text like 'MD: H59 to H65' or 'MD: H57, H58, H59'"""
    if not text or not isinstance(text, str):
        return []

    codes = []
    text = text.upper().strip()

    # Pattern for ranges like "H59 to H65" or "H59-H65"
    range_pattern = r'([A-Z]+)(\d+)\s*(?:TO|-)\s*([A-Z]+)?(\d+)'
    range_matches = re.findall(range_pattern, text)

    for match in range_matches:
        prefix1, start_num, prefix2, end_num = match
        prefix2 = prefix2 or prefix1  # Use first prefix if second is empty

        try:
            start = int(start_num)
            end = int(end_num)

            # Generate all codes in the range
            for i in range(start, end + 1):
                codes.append(f"{prefix1}{i}")
        except ValueError:
            continue

    # Pattern for individual codes like "H59", "H60", etc.
    individual_pattern = r'\b([A-Z]+)(\d+)\b'
    individual_matches = re.findall(individual_pattern, text)

    for match in individual_matches:
        prefix, num = match
        codes.append(f"{prefix}{num}")

    # Remove duplicates while preserving order
    seen = set()
    unique_codes = []
    for code in codes:
        if code not in seen:
            seen.add(code)
            unique_codes.append(code)

    return unique_codes

def is_audio_code_match(row_text, search_code):
    """Check if an audio code matches within the text"""
    if not row_text or not search_code:
        return False

    search_code = search_code.upper().strip()
    extracted_codes = extract_audio_codes(row_text)

    return search_code in extracted_codes

def find_date_in_row(row_dict):
    """Find date values in a row, checking multiple possible columns"""
    date_columns = []

    # Check for columns that might contain dates
    for col_name, value in row_dict.items():
        col_lower = col_name.lower()
        if ('date' in col_lower or
            'audio' in col_lower or
            col_name in ['column_4', 'column_5'] or  # Common date column positions
            'unnamed' in col_lower):

            if value and str(value).strip():
                parsed_date = parse_date_string(str(value))
                if parsed_date:
                    date_columns.append((col_name, parsed_date))

    return date_columns

def find_audio_in_row(row_dict):
    """Find audio code values in a row, checking multiple possible columns"""
    audio_columns = []

    # Check for columns that might contain audio codes
    for col_name, value in row_dict.items():
        col_lower = col_name.lower()
        if ('audio' in col_lower or
            'md' in col_lower or
            'notes' in col_lower or
            col_name in ['column_7', 'column_8', 'column_9'] or  # Common audio column positions
            'unnamed' in col_lower):

            if value and str(value).strip():
                audio_codes = extract_audio_codes(str(value))
                if audio_codes:
                    audio_columns.append((col_name, audio_codes))

    return audio_columns

def is_date_match(row_date_str, search_date_str, date_range=9):
    """Check if a date matches within the specified range or falls within a date range"""
    row_date = parse_date_string(row_date_str)

    if not row_date:
        return False

    # Check if search_date_str is a date range (contains "to")
    if " to " in search_date_str:
        try:
            start_date_str, end_date_str = search_date_str.split(" to ")
            start_date = parse_date_string(start_date_str)
            end_date = parse_date_string(end_date_str)

            if not start_date or not end_date:
                return False

            # Check if row_date is within the range (inclusive)
            return start_date <= row_date <= end_date
        except:
            # If there's an error parsing the date range, fall back to single date search
            pass

    # Single date search with range
    search_date = parse_date_string(search_date_str)

    if not search_date:
        return False

    # Calculate date difference
    delta = abs((row_date - search_date).days)

    # Return True if within range
    return delta <= date_range

def calculate_similarity(str1, str2):
    """Calculate string similarity ratio"""
    if not str1 or not str2:
        return 0
    return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()

def search_data(query, sheet_filter=None, column_filter=None, sort_by=None, sort_order='asc',
                match_type='partial', date_search=None, date_range=9, multi_keywords=False):
    """Search across all cached data with advanced options"""
    if not cached_data:
        return []

    results = []
    sheets_to_search = [sheet_filter] if sheet_filter else cached_data.keys()

    # Process query
    query = query.lower().strip()
    keywords = query.split() if multi_keywords else [query]

    # Check if query looks like a date
    is_date_query = False
    search_date = None
    if date_search:
        search_date = parse_date_string(date_search)
        is_date_query = search_date is not None
    elif re.search(r'\d{1,2}[-/\s.]\w{3,9}[-/\s.]\d{2,4}|\d{4}[-/\s.]\d{1,2}[-/\s.]\d{1,2}', query):
        search_date = parse_date_string(query)
        is_date_query = search_date is not None

    # Check if query looks like an audio code
    is_audio_code_query = False
    if re.search(r'^[A-Za-z]+\d+$', query):
        is_audio_code_query = True

    for sheet_name in sheets_to_search:
        if sheet_name not in cached_data:
            continue

        df = cached_data[sheet_name]

        # Create a copy of the dataframe for searching
        search_df = df.copy()

        # Add normalized audio column for better matching
        if 'audio' in search_df.columns:
            search_df['normalized_audio'] = search_df['audio'].apply(normalize_audio_value)

        # Process each row
        for _, row in search_df.iterrows():
            row_dict = row.to_dict()
            match_found = False

            # Special handling for date queries
            if is_date_query and search_date:
                # Find all date columns in the row
                date_columns = find_date_in_row(row_dict)
                date_match = False

                for _, row_date in date_columns:
                    # Calculate date difference
                    delta = abs((row_date - search_date).days)
                    if delta <= date_range:
                        date_match = True
                        break

                if date_match:
                    match_found = True

            # Special handling for audio code queries
            elif is_audio_code_query:
                # Find all audio columns in the row
                audio_columns = find_audio_in_row(row_dict)
                audio_match = False

                for _, audio_codes in audio_columns:
                    if query.upper() in audio_codes:
                        audio_match = True
                        break

                if audio_match:
                    match_found = True

            # Standard search if not a special query type or if special search didn't find a match
            elif not match_found:
                # Column-specific search
                if column_filter and column_filter in row_dict:
                    col_value = str(row_dict.get(column_filter, '')).lower()

                    if column_filter == 'audio' and 'normalized_audio' in row_dict:
                        col_value = str(row_dict.get('normalized_audio', '')).lower()

                    # Multi-keyword search
                    if multi_keywords:
                        all_keywords_found = True
                        for keyword in keywords:
                            if match_type == 'exact':
                                if keyword != col_value and keyword not in col_value.split():
                                    all_keywords_found = False
                                    break
                            else:  # partial match
                                if keyword not in col_value:
                                    all_keywords_found = False
                                    break

                        match_found = all_keywords_found
                    else:
                        # Single keyword search
                        if match_type == 'exact':
                            match_found = (query == col_value or query in col_value.split())
                        else:  # partial match
                            match_found = query in col_value
                else:
                    # Search across all columns
                    if multi_keywords:
                        # Check if all keywords are found in any column
                        all_keywords_found = True
                        for keyword in keywords:
                            keyword_found = False

                            for col, value in row_dict.items():
                                col_value = str(value).lower()

                                if match_type == 'exact':
                                    if keyword == col_value or keyword in col_value.split():
                                        keyword_found = True
                                        break
                                else:  # partial match
                                    if keyword in col_value:
                                        keyword_found = True
                                        break

                            if not keyword_found:
                                all_keywords_found = False
                                break

                        match_found = all_keywords_found
                    else:
                        # Check if the single query is found in any column
                        for col, value in row_dict.items():
                            col_value = str(value).lower()

                            if match_type == 'exact':
                                if query == col_value or query in col_value.split():
                                    match_found = True
                                    break
                            else:  # partial match
                                if query in col_value:
                                    match_found = True
                                    break

            if match_found:
                # Remove the normalized_audio column before adding to results
                if 'normalized_audio' in row_dict:
                    del row_dict['normalized_audio']

                results.append(row_dict)

    # Sort results
    if sort_by and results:
        reverse = sort_order == 'desc'
        try:
            results.sort(key=lambda x: x.get(sort_by, ''), reverse=reverse)
        except:
            pass  # Skip sorting if there's an error

    return results

# Authentication Routes
@app.route('/register', methods=['GET', 'POST'])
def register():
    """User registration"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')

        # Validation
        if not username or not email or not password:
            flash('All fields are required.', 'error')
            return render_template('register.html')

        if password != confirm_password:
            flash('Passwords do not match.', 'error')
            return render_template('register.html')

        if len(password) < 6:
            flash('Password must be at least 6 characters long.', 'error')
            return render_template('register.html')

        # Check if user already exists
        if User.get_by_username(username):
            flash('Username already exists.', 'error')
            return render_template('register.html')

        # Create new user
        user_id = str(datetime.now().timestamp()).replace('.', '')
        password_hash = generate_password_hash(password)

        new_user = User(
            id=user_id,
            username=username,
            email=email,
            password_hash=password_hash,
            is_admin=False
        )

        try:
            new_user.save()
            log_user_activity('user_registered', f'Username: {username}')
            flash('Registration successful! Please log in.', 'success')
            return redirect(url_for('login'))
        except Exception as e:
            logger.error(f"Registration error: {e}")
            flash('Registration failed. Please try again.', 'error')

    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')

        if not username or not password:
            flash('Username and password are required.', 'error')
            return render_template('login.html')

        user = User.get_by_username(username)

        if user and user.check_password(password):
            login_user(user)
            log_user_activity('user_login', f'Username: {username}')
            flash(f'Welcome back, {username}!', 'success')

            # Redirect to next page or home
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('home'))
        else:
            log_user_activity('failed_login', f'Username: {username}')
            flash('Invalid username or password.', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """User logout"""
    username = current_user.username
    logout_user()
    log_user_activity('user_logout', f'Username: {username}')
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/')
@login_required
def home():
    """Home page"""
    log_user_activity('page_view', 'home')

    # Get system stats
    stats = {
        'total_sheets': len(sheet_names),
        'total_records': sum(len(df) for df in cached_data.values()) if cached_data else 0,
        'last_processed': last_processed_file,
        'user_count': get_user_count(),
        'is_admin': current_user.is_admin
    }

    return render_template('home.html', stats=stats, sheet_names=sheet_names)

@app.route('/search')
@login_required
def search():
    """Search page"""
    log_user_activity('page_view', 'search')
    return render_template('search.html', sheet_names=sheet_names)

@app.route('/api/search', methods=['POST'])
@login_required
def api_search():
    """API endpoint for search"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        sheet_filter = data.get('sheet_filter')
        column_filter = data.get('column_filter')
        sort_by = data.get('sort_by')
        sort_order = data.get('sort_order', 'asc')
        match_type = data.get('match_type', 'partial')  # 'exact' or 'partial'
        date_search = data.get('date_search')  # Date string to search for
        date_range = int(data.get('date_range', 9))  # Days before/after date
        multi_keywords = data.get('multi_keywords', False)  # Search for multiple keywords

        if not query:
            return jsonify({'error': 'Search query is required'}), 400

        # Log search activity
        log_details = (
            f'Query: {query}, Sheet: {sheet_filter}, Column: {column_filter}, '
            f'Match Type: {match_type}, Multi-keywords: {multi_keywords}'
        )
        if date_search:
            log_details += f', Date Search: {date_search} (±{date_range} days)'

        log_user_activity('search', log_details)

        # Perform search with advanced options
        results = search_data(
            query=query,
            sheet_filter=sheet_filter,
            column_filter=column_filter,
            sort_by=sort_by,
            sort_order=sort_order,
            match_type=match_type,
            date_search=date_search,
            date_range=date_range,
            multi_keywords=multi_keywords
        )

        return jsonify({
            'results': results[:1000],  # Limit to 1000 results for performance
            'total_count': len(results),
            'query': query,
            'filters': {
                'sheet': sheet_filter,
                'column': column_filter,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'match_type': match_type,
                'date_search': date_search,
                'date_range': date_range,
                'multi_keywords': multi_keywords
            }
        })

    except Exception as e:
        logger.error(f"Search API error: {e}")
        return jsonify({'error': 'Search failed'}), 500

@app.route('/results')
@login_required
def results():
    """Results page"""
    return render_template('results.html')

# Admin Routes
@app.route('/admin')
@login_required
@admin_required
def admin():
    """Admin dashboard"""
    log_user_activity('page_view', 'admin')

    # Get admin stats
    stats = {
        'total_users': get_user_count(),
        'total_sheets': len(sheet_names),
        'total_records': sum(len(df) for df in cached_data.values()) if cached_data else 0,
        'last_processed': last_processed_file,
        'recent_activity': get_recent_activity(50)
    }

    return render_template('admin.html', stats=stats)

@app.route('/admin/upload', methods=['POST'])
@login_required
@admin_required
def admin_upload():
    """Admin file upload"""
    if 'file' not in request.files:
        flash('No file selected.', 'error')
        return redirect(url_for('admin'))

    file = request.files['file']
    if file.filename == '':
        flash('No file selected.', 'error')
        return redirect(url_for('admin'))

    if not file.filename.lower().endswith(('.xlsx', '.xls')):
        flash('Please upload an Excel file (.xlsx or .xls).', 'error')
        return redirect(url_for('admin'))

    try:
        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        # Process the file
        success, message = process_excel_file(file_path)

        if success:
            log_user_activity('file_upload', f'File: {filename}, Message: {message}')
            flash(f'File uploaded and processed successfully! {message}', 'success')
        else:
            log_user_activity('file_upload_failed', f'File: {filename}, Error: {message}')
            flash(f'File upload failed: {message}', 'error')

    except Exception as e:
        logger.error(f"File upload error: {e}")
        flash('File upload failed due to an unexpected error.', 'error')

    return redirect(url_for('admin'))

@app.route('/admin/date-analysis')
@login_required
@admin_required
def admin_date_analysis():
    """Admin page to view date analysis report"""
    log_user_activity('page_view', 'admin_date_analysis')

    try:
        analysis_path = os.path.join(app.config['DATA_FOLDER'], 'date_analysis.json')
        if os.path.exists(analysis_path):
            with open(analysis_path, 'r', encoding='utf-8') as f:
                date_analysis = json.load(f)
        else:
            date_analysis = {}

        # Calculate summary statistics
        total_sheets = len(date_analysis)
        sheets_with_dates = sum(1 for info in date_analysis.values() if info['dates_processed'] > 0)
        total_dates_processed = sum(info['dates_processed'] for info in date_analysis.values())

        summary = {
            'total_sheets': total_sheets,
            'sheets_with_dates': sheets_with_dates,
            'sheets_without_dates': total_sheets - sheets_with_dates,
            'total_dates_processed': total_dates_processed
        }

        return render_template('admin_date_analysis.html',
                             date_analysis=date_analysis,
                             summary=summary)

    except Exception as e:
        logger.error(f"Error loading date analysis: {e}")
        flash('Error loading date analysis report.', 'error')
        return redirect(url_for('admin'))

def get_user_count():
    """Get total number of users"""
    try:
        with open('users.csv', 'r', newline='', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            return sum(1 for _ in reader)
    except FileNotFoundError:
        return 0

def get_recent_activity(limit=50):
    """Get recent user activity"""
    try:
        with open('logs.csv', 'r', newline='', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            activities = list(reader)
            return sorted(activities, key=lambda x: x['timestamp'], reverse=True)[:limit]
    except FileNotFoundError:
        return []

# Error Handlers
@app.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}")
    return render_template('500.html'), 500

# Initialize application
def initialize_app():
    """Initialize the application with default data"""
    logger.info("Initializing Archives Raw Data Finder application...")

    # Create default admin user if no users exist
    if get_user_count() == 0:
        admin_user = User(
            id='1',
            username='admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123'),
            is_admin=True
        )
        admin_user.save()
        logger.info("Created default admin user (username: admin, password: admin123)")

    # Process existing Excel file if it exists
    excel_file = 'Sheet to analyze.xlsx'
    if os.path.exists(excel_file) and not cached_data:
        logger.info(f"Found existing Excel file: {excel_file}")
        success, message = process_excel_file(excel_file)
        if success:
            logger.info(f"Successfully processed existing Excel file: {message}")
        else:
            logger.error(f"Failed to process existing Excel file: {message}")

    logger.info("Application initialization completed")

if __name__ == '__main__':
    # Initialize the application
    initialize_app()

    # Run the Flask app
    logger.info("Starting Archives Raw Data Finder on port 5006...")
    app.run(host='0.0.0.0', port=5006, debug=False)
