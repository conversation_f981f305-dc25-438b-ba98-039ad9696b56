#!/usr/bin/env python3
"""
Date Standardization Utility for Archives Raw Data Finder

This script analyzes all 48 sheets in the Excel file, identifies date columns,
and standardizes all date entries to DD-MMM-YYYY format.

Usage: python standardize_dates.py
"""

import os
import sys
import pandas as pd
import json
from datetime import datetime
from dateutil import parser

def standardize_date_format(date_str):
    """Convert date string to DD-MMM-YYYY format"""
    if not date_str or not isinstance(date_str, str) or date_str.strip() == '':
        return ''
    
    date_str = date_str.strip()
    if not date_str:
        return ''
    
    try:
        # Parse the date using dateutil.parser
        parsed_date = parser.parse(date_str, fuzzy=True)
        
        # If the year is less than 100, assume it's a 2-digit year
        if parsed_date.year < 100:
            # Assume years 00-30 are 2000-2030, 31-99 are 1931-1999
            if parsed_date.year <= 30:
                parsed_date = parsed_date.replace(year=parsed_date.year + 2000)
            else:
                parsed_date = parsed_date.replace(year=parsed_date.year + 1900)
        
        # Format as DD-MMM-YYYY
        return parsed_date.strftime('%d-%b-%Y')
    except:
        # If parsing fails, return original string
        return date_str

def identify_date_column(df, sheet_name):
    """Identify which column contains dates in a sheet"""
    # Check columns D and E first (indices 3 and 4)
    potential_columns = []
    
    # Add column D and E by index
    if len(df.columns) > 3:
        potential_columns.append((3, df.columns[3]))
    if len(df.columns) > 4:
        potential_columns.append((4, df.columns[4]))
    
    # Also check columns with date-related names
    for i, col_name in enumerate(df.columns):
        col_lower = str(col_name).lower()
        if ('date' in col_lower or 
            'time' in col_lower or 
            'day' in col_lower or
            'month' in col_lower or
            'year' in col_lower):
            potential_columns.append((i, col_name))
    
    # Remove duplicates
    potential_columns = list(set(potential_columns))
    
    # Test each potential column for date content
    best_score = 0
    best_column = None
    best_index = None
    
    for col_index, col_name in potential_columns:
        if col_index >= len(df.columns):
            continue
            
        # Sample some values from the column
        sample_values = df.iloc[:min(50, len(df)), col_index].dropna()
        date_count = 0
        total_count = 0
        
        for value in sample_values:
            if value and str(value).strip():
                total_count += 1
                # Try to parse as date
                try:
                    parsed = parser.parse(str(value), fuzzy=True)
                    # Check if it looks like a reasonable date
                    if 1900 <= parsed.year <= 2030:
                        date_count += 1
                except:
                    pass
        
        # Calculate score (percentage of values that are dates)
        if total_count > 0:
            score = date_count / total_count
            if score > best_score and score > 0.3:  # At least 30% should be dates
                best_score = score
                best_column = col_name
                best_index = col_index
    
    print(f"Sheet '{sheet_name}': Date column identified as '{best_column}' (index {best_index}) with {best_score:.2%} date content")
    
    return best_column, best_index

def main():
    """Main function to standardize dates in all sheets"""
    excel_file = 'Sheet to analyze.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"Error: Excel file '{excel_file}' not found!")
        sys.exit(1)
    
    print("=" * 80)
    print("ARCHIVES RAW DATA FINDER - DATE STANDARDIZATION UTILITY")
    print("=" * 80)
    print(f"Processing Excel file: {excel_file}")
    print(f"Target format: DD-MMM-YYYY (e.g., 04-May-2001)")
    print("=" * 80)
    
    try:
        # Read Excel file
        xl = pd.ExcelFile(excel_file)
        sheet_names = [name for name in xl.sheet_names if name not in ['Sheet2', 'Sheet3']]
        
        print(f"Found {len(sheet_names)} sheets to process")
        print()
        
        date_analysis = {}
        total_dates_processed = 0
        sheets_with_dates = 0
        
        for i, sheet_name in enumerate(sheet_names, 1):
            print(f"[{i:2d}/{len(sheet_names)}] Processing sheet: {sheet_name}")
            
            # Read sheet
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            if df.empty:
                print(f"    ⚠️  Sheet is empty, skipping...")
                continue
            
            # Use first row as headers if current headers are mostly "Unnamed"
            if df.columns[0].startswith('Category') or 'Unnamed' in str(df.columns[1]):
                # First row contains the actual headers
                new_headers = df.iloc[0].tolist()
                df = df.iloc[1:].reset_index(drop=True)
                df.columns = new_headers
            
            # Identify date column
            original_date_column, date_column_index = identify_date_column(df, sheet_name)
            
            if date_column_index is not None and date_column_index < len(df.columns):
                print(f"    📅 Date column found: '{original_date_column}' (Column {chr(65 + date_column_index)})")
                
                # Count and standardize dates
                original_dates = []
                standardized_dates = []
                
                for j, value in enumerate(df.iloc[:, date_column_index]):
                    if value and str(value).strip():
                        original_date = str(value).strip()
                        standardized_date = standardize_date_format(original_date)
                        
                        if original_date != standardized_date:
                            original_dates.append(original_date)
                            standardized_dates.append(standardized_date)
                        
                        df.iloc[j, date_column_index] = standardized_date
                
                # Store analysis info
                date_analysis[sheet_name] = {
                    'column_name': original_date_column,
                    'column_index': date_column_index,
                    'column_letter': chr(65 + date_column_index),
                    'dates_processed': len(original_dates),
                    'sample_conversions': list(zip(original_dates[:5], standardized_dates[:5]))
                }
                
                total_dates_processed += len(original_dates)
                sheets_with_dates += 1
                
                print(f"    ✅ Standardized {len(original_dates)} dates")
                
                # Show sample conversions
                if original_dates:
                    print(f"    📝 Sample conversions:")
                    for orig, std in list(zip(original_dates, standardized_dates))[:3]:
                        print(f"       '{orig}' → '{std}'")
            else:
                date_analysis[sheet_name] = {
                    'column_name': None,
                    'column_index': None,
                    'column_letter': None,
                    'dates_processed': 0,
                    'sample_conversions': []
                }
                print(f"    ❌ No date column identified")
            
            print()
        
        # Save analysis report
        os.makedirs('data', exist_ok=True)
        analysis_path = 'data/date_analysis.json'
        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(date_analysis, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("=" * 80)
        print("STANDARDIZATION COMPLETE")
        print("=" * 80)
        print(f"Total sheets processed: {len(sheet_names)}")
        print(f"Sheets with dates: {sheets_with_dates}")
        print(f"Sheets without dates: {len(sheet_names) - sheets_with_dates}")
        print(f"Total dates standardized: {total_dates_processed:,}")
        print(f"Analysis report saved to: {analysis_path}")
        print("=" * 80)
        
        # Show sheets with dates
        if sheets_with_dates > 0:
            print("\nSHEETS WITH DATE COLUMNS:")
            print("-" * 50)
            for sheet_name, info in date_analysis.items():
                if info['dates_processed'] > 0:
                    print(f"{sheet_name:30} | Column {info['column_letter']} | {info['dates_processed']:4d} dates")
        
        # Show sheets without dates
        sheets_without_dates = [name for name, info in date_analysis.items() if info['dates_processed'] == 0]
        if sheets_without_dates:
            print(f"\nSHEETS WITHOUT DATE COLUMNS ({len(sheets_without_dates)}):")
            print("-" * 50)
            for sheet_name in sheets_without_dates:
                print(f"  • {sheet_name}")
        
        print("\n✅ Date standardization completed successfully!")
        
    except Exception as e:
        print(f"❌ Error processing Excel file: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
